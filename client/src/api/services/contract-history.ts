import type {
  ContractHistoryDetail,
  ContractHistoryListItem,
  HistoryQueryParams,
  UpdateHistoryTitleParams,
} from '@/types/api';
import type { PaginatedResponse, ResultResponse } from '@/types/common';

import api from '../api';

const API_PATH = '/contract/history';

/**
 * 合同历史记录API服务
 */
export const contractHistoryApi = {
  /**
   * 获取历史记录列表（支持分页和过滤）
   * @param params 查询参数
   * @returns 分页的历史记录列表
   */
  getHistoryList: async (
    params: HistoryQueryParams = {},
  ): Promise<ResultResponse<PaginatedResponse<ContractHistoryListItem>>> => {
    return api.post(`${API_PATH}`, params);
  },

  /**
   * 根据会话ID获取历史记录详情
   * @param sessionId 会话ID
   * @returns 历史记录详情
   */
  getHistoryDetail: async (sessionId: string): Promise<ResultResponse<ContractHistoryDetail>> => {
    return api.get(`${API_PATH}/${sessionId}`);
  },

  /**
   * 根据会话ID删除历史记录
   * @param sessionId 会话ID
   * @returns 删除结果
   */
  deleteHistory: async (sessionId: string): Promise<ResultResponse<void>> => {
    return api.delete(`${API_PATH}/${sessionId}`);
  },

  /**
   * 根据会话ID更新历史记录标题
   * @param sessionId 会话ID
   * @param params 更新参数
   * @returns 更新结果
   */
  updateHistoryTitle: async (
    sessionId: string,
    params: UpdateHistoryTitleParams,
  ): Promise<ResultResponse<ContractHistoryDetail>> => {
    return api.patch(`${API_PATH}/${sessionId}/title`, params);
  },
};

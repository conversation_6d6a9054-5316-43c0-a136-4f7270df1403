import { v4 as uuid } from 'uuid';

import { API_URL } from '@/shared/config';
import { getTokenFromLocalStorage } from '@/shared/utils/localStorageUtil.ts';
import { parseStrToJson } from '@/shared/utils/stringUtil';
import type { ResultResponse } from '@/types/common';

import api from '../api';

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  template_markdown: string;
  selected_template_name: string;
}

export const contractApi = {
  /**
   * 生成合同预览 - 使用SSE流式接口
   * @param userInput 用户输入
   * @param onLog 日志回调函数
   * @returns 合同生成结果
   */
  async generateContractPreview(
    userInput: string,
    onLog: (log: string) => void,
  ): Promise<ResultResponse<ContractGenerationResult>> {
    return new Promise((resolve) => {
      // 第一步：生成sessionId
      const sessionId = uuid();
      // 获取token并添加到URL中
      const token = getTokenFromLocalStorage();
      // 构建完整URL，包含token
      const url = `${API_URL}contract/stream?sessionId=${sessionId}${token ? `&token=${token}` : ''}`;
      onLog('正在建立SSE连接...');

      // 第二步：建立SSE连接，传递sessionId
      let eventSource = new EventSource(url);
      let haspost = false;

      const disposeEventSource = (result: ResultResponse<ContractGenerationResult>) => {
        eventSource?.close();
        eventSource = undefined!;
        resolve(result);
      };

      eventSource.onopen = () => {
        onLog('SSE连接已建立');
        api.post('contract/generate_contract', { sessionId, userInput }).then((result) => {
          const { success, message } = result as unknown as ResultResponse;
          haspost = success;
          if (!success) {
            onLog(message);
            disposeEventSource({ success: false, message });
          }
        });
      };

      eventSource.onmessage = (event) => {
        const data = parseStrToJson(event.data, {});
        data.log && onLog(data.log);
        if ('success' in data) {
          disposeEventSource(data);
        }
      };

      eventSource.onerror = (event) => {
        console.error('SSE连接错误', event);
        const msg = haspost
          ? 'SSE连接断开,您将无法继续收到消息,但您的任务仍在后台继续运行,可以稍后刷新界面查看结果'
          : 'SSE连接失败, 请检查您的网络';
        onLog(msg);
        disposeEventSource({ success: false, message: msg });
      };
    });
  },
};

import { BehaviorSubject } from 'rxjs';

import { contractApi } from '@/api/services/contract';
import { EXAMPLE_INPUT } from '@/shared/config.ts';
import type { ContractHistoryDetail } from '@/types/api';

import { SidebarController } from './sidebar';

// 生成状态枚举
export enum GenerationStatus {
  IDLE = 'idle',
  GENERATING = 'generating',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  template_markdown: string;
  selected_template_name: string;
}

export class HomeController {
  // 基础状态
  public userInput$ = new BehaviorSubject<string>('');
  public generationStatus$ = new BehaviorSubject<GenerationStatus>(GenerationStatus.IDLE);
  public generationLogs$ = new BehaviorSubject<string[]>([]);
  public contractResult$ = new BehaviorSubject<ContractGenerationResult | null>(null);
  public contractText$ = new BehaviorSubject<string>('');

  // 布局状态：控制是否显示左右分栏布局
  public showSplitLayout$ = new BehaviorSubject<boolean>(false);
  // 查看日志
  public viewLogs$ = new BehaviorSubject<boolean>(false);
  // 比对模态框状态
  public showDiffModal$ = new BehaviorSubject<boolean>(false);

  // 子控制器
  public sidebarController = new SidebarController(this);

  public constructor() {}

  // 更新用户输入
  public updateUserInput(input: string): void {
    this.userInput$.next(input);
  }

  // 生成合同
  public async generateContract(): Promise<void> {
    let userInput = this.userInput$.getValue();
    if (!userInput.trim()) {
      userInput = EXAMPLE_INPUT;
      this.userInput$.next(userInput);
    }

    // 切换到分栏布局
    this.showSplitLayout$.next(true);
    this.sidebarController.toggleSidebarCollapsed();

    this.generationStatus$.next(GenerationStatus.GENERATING);
    this.generationLogs$.next([]);
    this.contractResult$.next(null);
    this.contractText$.next('');

    const response = await contractApi.generateContractPreview(userInput, (log: string) => {
      // 处理实时日志
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, log]);
    });

    if (response.success) {
      // 先添加完成日志，再设置结果（这样可以确保日志显示完整）
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, '合同生成完成']);

      // 设置结果和状态（这会触发UI切换到markdown预览）
      this.contractResult$.next(response.data);
      this.contractText$.next(response.data.contract_markdown);
      this.generationStatus$.next(GenerationStatus.SUCCESS);
    } else {
      this.generationStatus$.next(GenerationStatus.ERROR);
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, '合同生成失败']);
    }
  }

  // 下载合同
  public async downloadContract(): Promise<void> {
    // TODO
  }

  // 和模版进行比对
  public completeWithTemplate() {
    const contractResult = this.contractResult$.getValue();
    if (contractResult && contractResult.template_markdown) {
      this.showDiffModal$.next(true);
    }
  }

  public toggleViewLogs(visible: boolean) {
    this.viewLogs$.next(visible);
  }

  // 切换比对模态框显示状态
  public toggleDiffModal(visible: boolean) {
    this.showDiffModal$.next(visible);
  }

  // 清空结果
  public clearResults(): void {
    this.contractResult$.next(null);
    this.viewLogs$.next(false);
    this.contractText$.next('');
    this.generationLogs$.next([]);
    this.generationStatus$.next(GenerationStatus.IDLE);
  }

  // 从历史记录加载数据
  public loadFromHistory(historyData: ContractHistoryDetail): void {
    // 设置用户输入
    this.updateUserInput(historyData.userInput);

    // 设置合同结果
    const contractResult: ContractGenerationResult = {
      contract_markdown: historyData.contractMarkdown,
      template_markdown: historyData.templateMarkdown || '',
      selected_template_name: historyData.selectedTemplateName || '',
    };

    // 设置状态
    this.contractResult$.next(contractResult);
    this.contractText$.next(historyData.contractMarkdown);
    this.generationStatus$.next(GenerationStatus.SUCCESS);

    // 设置日志（解析后端日志）
    const logs = historyData.backendLogs.split('\n').filter((log: string) => log.trim());
    this.generationLogs$.next(logs);

    // 切换到分栏布局
    this.showSplitLayout$.next(true);
  }

  // 重置到初始状态
  public resetToInitialState(): void {
    this.showSplitLayout$.next(false);
    this.clearResults();
    this.updateUserInput('');
  }

  // 销毁
  public destroy() {
    this.userInput$.complete();
    this.generationStatus$.complete();
    this.generationLogs$.complete();
    this.generationLogs$.next([]);
    this.contractResult$.complete();
    this.contractResult$.next(null);
    this.contractText$.complete();
    this.showSplitLayout$.complete();
    this.showDiffModal$.complete();
    this.sidebarController?.destroy();
    this.sidebarController = null!;
  }
}

import { FC, MouseEvent, memo } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  AlertTriangle,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  PlusCircle,
  ShieldCheck,
  Trash2,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getAnalysisSceneTypeIcon, getAnalysisSceneTypeLabel } from '@/scenes/common/constants.tsx';
import { useObservableState } from '@/shared/hooks/useObservableState';
import { formatFileSize } from '@/shared/utils/stringUtil';

import type { HistoryItem as HistoryItemType } from '../common/VideoAnalysisDB';

import type { SidebarController } from './SidebarController';

interface SidebarProps {
  controller: SidebarController;
}

// 单个历史记录项组件
const HistoryItem = memo<{
  item: HistoryItemType;
  onLoad: (id: string) => void;
  onDelete: (e: MouseEvent, id: string) => void;
}>(({ item, onLoad, onDelete }) => {
  const SceneTypeIcon = getAnalysisSceneTypeIcon(item.sceneType);
  const sceneTypeLabel = getAnalysisSceneTypeLabel(item.sceneType);

  return (
    <div
      key={item.id}
      className="group mx-2 mb-2 cursor-pointer rounded-lg border border-slate-100 bg-white p-3 transition-all hover:border-blue-100 hover:bg-blue-50"
      onClick={() => onLoad(item.id)}
    >
      {/* 头部：标题和操作按钮 */}
      <div className="mb-2 flex items-center justify-between">
        <div className="flex max-w-[180px] items-center gap-1.5">
          <SceneTypeIcon className="size-6" />
          <span className="truncate font-medium text-sm">{item.title}</span>
        </div>
        <div className="flex items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="text-red-500 hover:text-red-600"
                  onClick={(e) => onDelete(e, item.id)}
                >
                  <Trash2 className="size-3.5" />
                </button>
              </TooltipTrigger>
              <TooltipContent side="right" className="text-xs">
                删除记录
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 场景类型和文件大小 */}
      <div className="mb-2 flex items-center justify-between">
        <Badge variant="outline" className={`h-5 px-1.5 py-0 text-xs`}>
          {sceneTypeLabel}
        </Badge>
        <span className="text-xs text-slate-500">{formatFileSize(item.fileSize)}</span>
      </div>

      {/* 时间信息 */}
      <div className="flex items-center text-xs text-slate-400">
        <Calendar className="mr-1.5 size-3" />
        <span>
          {formatDistanceToNow(new Date(item.date), {
            locale: zhCN,
            addSuffix: true,
          })}
        </span>
      </div>
    </div>
  );
});

// 历史记录列表组件
const HistoryList = memo<{ controller: SidebarController }>(({ controller }) => {
  const permissionGranted = useObservableState(controller.storagePermissionGranted$);
  const historyItems = useObservableState(controller.historyItems$);
  const loading = useObservableState(controller.historyLoading$);

  const handleLoadItem = (id: string) => {
    controller.loadHistoryItem(id);
  };

  const handleDeleteItem = (e: MouseEvent, id: string) => {
    e.stopPropagation();
    if (confirm('确定要删除这条历史记录吗？此操作不可恢复。')) {
      controller.deleteHistoryItem(id);
    }
  };

  if (!permissionGranted) {
    return <div className="px-4 py-2 text-xs italic text-[#bfbfbf]">未设置存储目录</div>;
  }

  if (loading) {
    return (
      <div className="flex items-center px-4 py-2 text-sm text-[#595959]">
        <div className="mr-2 size-3 animate-pulse rounded-full bg-blue-500 opacity-75"></div>
        正在加载...
      </div>
    );
  }

  if (historyItems.length === 0) {
    return <div className="px-4 py-2 text-sm text-[#a5a5a5]">暂无历史记录</div>;
  }

  return (
    <div className="space-y-1">
      {historyItems.map((item) => (
        <HistoryItem
          key={item.id}
          item={item}
          onLoad={handleLoadItem}
          onDelete={handleDeleteItem}
        />
      ))}

      {permissionGranted && historyItems.length > 0 && (
        <div className="mt-2 border-t border-[#f5f5f5] px-4 py-3 text-xs text-slate-400">
          <div className="flex items-start">
            <AlertTriangle className="mr-1.5 mt-0.5 size-3 shrink-0" />
            <div>所有分析数据均保存在本地，请定期备份重要的分析记录。</div>
          </div>
        </div>
      )}
    </div>
  );
});

// 存储信息组件
const StorageInfo = memo<SidebarProps>(({ controller }) => {
  const initializing = useObservableState(controller.storageInitializing$);
  const permissionGranted = useObservableState(controller.storagePermissionGranted$);
  const directoryPath = useObservableState(controller.storageDirectoryPath$);

  // 如果正在初始化，则不显示
  if (initializing) {
    return null;
  }

  // 如果未授予权限，则显示提示
  if (!permissionGranted) {
    return (
      <div className="mx-4 mb-3 rounded-md border border-amber-200 bg-amber-50 p-3 text-xs text-amber-800">
        <div className="mb-1 flex items-start">
          <AlertTriangle className="mr-1.5 mt-0.5 size-3.5 shrink-0" />
          <div className="font-medium">需要设置本地存储目录</div>
        </div>
        <p className="mb-2 ml-5">您需要选择一个文件夹来存储分析结果。</p>
        <Button
          size="sm"
          variant="outline"
          className="ml-5 h-7 border-amber-300 bg-amber-100 text-xs hover:bg-amber-200"
          onClick={() => controller.requestStorageDirectory()}
        >
          选择存储文件夹
        </Button>
      </div>
    );
  }

  // 如果已授予权限，则显示当前存储目录
  if (directoryPath) {
    return (
      <div className="mx-4 mb-3 rounded-md border border-blue-100 bg-blue-50 p-2 text-xs text-blue-700">
        <div className="flex items-start">
          <ShieldCheck className="mr-1.5 mt-0.5 size-3.5 shrink-0" />
          <div>
            <div className="font-medium">当前存储目录</div>
            <div className="mt-1 truncate" title={directoryPath}>
              {directoryPath}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
});

// 折叠状态的侧边栏组件
const CollapsedSidebar = memo<SidebarProps>(({ controller }) => {
  return (
    <div className="flex h-full bg-[#f7f7f9] flex-col border-r border-[#f0f0f0] transition-all duration-300 w-16">
      {/* 新建分析按钮 */}
      <div className="p-4">
        <button
          onClick={() => controller.handleNewAnalysis()}
          className="flex h-[32px] w-full items-center justify-center rounded-lg border border-[#f0f0f0] bg-white text-[#595959] transition-colors hover:border-[#1890ff] hover:text-[#1890ff]"
        >
          <PlusCircle className="size-4" />
        </button>
      </div>

      {/* 历史按钮 */}
      <div className="flex-1 overflow-auto">
        <div className="flex items-center justify-center px-4 py-2 text-[#595959]">
          <button
            onClick={() => controller.expandAndLoadHistory()}
            className="flex size-8 items-center justify-center text-[#595959] transition-colors hover:text-[#1890ff]"
          >
            <Clock className="size-4" />
          </button>
        </div>
      </div>

      {/* 展开按钮 */}
      <div className="border-t border-[#f0f0f0] p-4">
        <button
          onClick={() => controller.toggleSidebarCollapsed()}
          className="flex justify-center items-center text-[#595959] transition-colors hover:text-[#1890ff]"
        >
          <ChevronRight className="size-4" />
        </button>
      </div>
    </div>
  );
});

// 展开状态的侧边栏组件
const ExpandedSidebar = memo<SidebarProps>(({ controller }) => {
  return (
    <div className="flex h-full bg-[#f7f7f9] flex-col border-r border-[#f0f0f0] transition-all duration-300 w-64">
      {/* 新建分析按钮 */}
      <div className="p-4">
        <button
          onClick={() => controller.handleNewAnalysis()}
          className="flex h-10 w-full items-center justify-center space-x-2 rounded-lg border border-[#f0f0f0] bg-white text-[#595959] transition-colors hover:border-[#1890ff] hover:text-[#1890ff]"
        >
          <PlusCircle className="size-4" />
          <span>视频分析</span>
        </button>
      </div>

      {/* 存储信息 */}
      <StorageInfo controller={controller} />

      {/* 历史记录区域 */}
      <div className="flex-1 overflow-auto">
        <div className="flex items-center justify-between px-4 py-2 text-[#595959]">
          <div className="flex items-center space-x-2">
            <Clock className="size-4" />
            <span>历史分析</span>
          </div>
          <Button variant="ghost" size="icon" className="size-6">
            <ChevronRight className="size-4" />
          </Button>
        </div>

        <div className="mt-1">
          <HistoryList controller={controller} />
        </div>
      </div>

      {/* 收起按钮 */}
      <div className="border-t border-[#f0f0f0] p-4">
        <button
          onClick={() => controller.toggleSidebarCollapsed()}
          className="w-full flex items-center text-[#595959] transition-colors hover:text-[#1890ff]"
        >
          <ChevronLeft className="size-4" />
          <span className="ml-2">收起侧栏</span>
        </button>
      </div>
    </div>
  );
});

// 主侧边栏组件
export const Sidebar: FC<SidebarProps> = memo(({ controller }) => {
  const collapsed = useObservableState(controller.sideBarCollapsed$);

  // 根据折叠状态选择不同的侧边栏组件
  return collapsed ? (
    <CollapsedSidebar controller={controller} />
  ) : (
    <ExpandedSidebar controller={controller} />
  );
});

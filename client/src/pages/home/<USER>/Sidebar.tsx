import { FC, MouseEvent, memo } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  FileText,
  PlusCircle,
  Trash2,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useObservableState } from '@/shared/hooks/useObservableState';
import type { ContractHistoryListItem } from '@/types/api';

import type { SidebarController } from './SidebarController';

interface SidebarProps {
  controller: SidebarController;
}

// 单个历史记录项组件
const HistoryItem = memo<{
  item: ContractHistoryListItem;
  onLoad: (sessionId: string) => void;
  onDelete: (e: MouseEvent, sessionId: string) => void;
}>(({ item, onLoad, onDelete }) => {
  return (
    <div
      key={item.id}
      className="group mx-2 mb-2 cursor-pointer rounded-lg border border-slate-100 bg-white p-3 transition-all hover:border-blue-100 hover:bg-blue-50"
      onClick={() => onLoad(item.sessionId)}
    >
      {/* 头部：标题和操作按钮 */}
      <div className="mb-3 flex items-start justify-between gap-2">
        <div className="flex min-w-0 flex-1 items-start gap-2">
          <FileText className="mt-0.5 size-3.5 text-blue-500 shrink-0" />
          <div className="min-w-0 flex-1">
            {/* 标题 - 最多2行 */}
            <h4 className="line-clamp-2 text-sm font-medium text-slate-900 leading-5">
              {item.title}
            </h4>
          </div>
        </div>
        <div className="flex items-center gap-1 opacity-0 transition-opacity group-hover:opacity-100 shrink-0">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="flex size-6 items-center justify-center rounded text-red-500 hover:bg-red-50 hover:text-red-600"
                  onClick={(e) => onDelete(e, item.sessionId)}
                >
                  <Trash2 className="size-3" />
                </button>
              </TooltipTrigger>
              <TooltipContent side="right" className="text-xs">
                删除记录
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 合同模板 - 最多1行 */}
      {item.selectedTemplateName && (
        <div className="mb-2">
          <Badge variant="outline" className="h-5 px-2 py-0 text-xs max-w-full">
            <span className="truncate">{item.selectedTemplateName}</span>
          </Badge>
        </div>
      )}

      {/* 创建时间 */}
      <div className="flex items-center text-xs text-slate-400">
        <Calendar className="mr-1.5 size-3 shrink-0" />
        <span className="truncate">
          {formatDistanceToNow(new Date(item.createdAt), {
            locale: zhCN,
            addSuffix: true,
          })}
        </span>
      </div>
    </div>
  );
});

// 历史记录列表组件
const HistoryList = memo<{ controller: SidebarController }>(({ controller }) => {
  const historyItems = useObservableState(controller.historyItems$);
  const loading = useObservableState(controller.historyLoading$);

  const handleLoadItem = (sessionId: string) => {
    controller.loadHistoryItem(sessionId);
  };

  const handleDeleteItem = (e: MouseEvent, sessionId: string) => {
    e.stopPropagation();
    if (confirm('确定要删除这条历史记录吗？此操作不可恢复。')) {
      controller.deleteHistoryItem(sessionId);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center px-4 py-2 text-sm text-[#595959]">
        <div className="mr-2 size-3 animate-pulse rounded-full bg-blue-500 opacity-75"></div>
        正在加载...
      </div>
    );
  }

  if (historyItems.length === 0) {
    return <div className="px-4 py-2 text-sm text-[#a5a5a5]">暂无历史记录</div>;
  }

  return (
    <div className="space-y-1">
      {historyItems.map((item) => (
        <HistoryItem
          key={item.id}
          item={item}
          onLoad={handleLoadItem}
          onDelete={handleDeleteItem}
        />
      ))}
    </div>
  );
});

// 折叠状态的侧边栏组件
const CollapsedSidebar = memo<SidebarProps>(({ controller }) => {
  return (
    <div className="flex h-full bg-[#f7f7f9] flex-col border-r border-[#f0f0f0] transition-all duration-300 w-16">
      {/* 新建分析按钮 */}
      <div className="p-4">
        <button
          onClick={() => controller.handleNewContract()}
          className="flex h-[32px] w-full items-center justify-center rounded-lg border border-[#f0f0f0] bg-white text-[#595959] transition-colors hover:border-[#1890ff] hover:text-[#1890ff]"
        >
          <PlusCircle className="size-4" />
        </button>
      </div>

      {/* 历史按钮 */}
      <div className="flex-1 overflow-auto">
        <div className="flex items-center justify-center px-4 py-2 text-[#595959]">
          <button
            onClick={() => controller.expandAndLoadHistory()}
            className="flex size-8 items-center justify-center text-[#595959] transition-colors hover:text-[#1890ff]"
          >
            <Clock className="size-4" />
          </button>
        </div>
      </div>

      {/* 展开按钮 */}
      <div className="border-t border-[#f0f0f0] p-4">
        <button
          onClick={() => controller.toggleSidebarCollapsed()}
          className="flex justify-center items-center text-[#595959] transition-colors hover:text-[#1890ff]"
        >
          <ChevronRight className="size-4" />
        </button>
      </div>
    </div>
  );
});

// 展开状态的侧边栏组件
const ExpandedSidebar = memo<SidebarProps>(({ controller }) => {
  return (
    <div className="flex h-full bg-[#f7f7f9] flex-col border-r border-[#f0f0f0] transition-all duration-300 w-64">
      {/* 新建分析按钮 */}
      <div className="p-4">
        <button
          onClick={() => controller.handleNewContract()}
          className="flex h-10 w-full items-center justify-center space-x-2 rounded-lg border border-[#f0f0f0] bg-white text-[#595959] transition-colors hover:border-[#1890ff] hover:text-[#1890ff]"
        >
          <PlusCircle className="size-4" />
          <span>新合同</span>
        </button>
      </div>

      {/* 历史记录区域 */}
      <div className="flex-1 overflow-auto">
        <div className="flex items-center justify-between px-4 py-2 text-[#595959]">
          <div className="flex items-center space-x-2">
            <Clock className="size-4" />
            <span>历史记录</span>
          </div>
          <Button variant="ghost" size="icon" className="size-6">
            <ChevronRight className="size-4" />
          </Button>
        </div>

        <div className="mt-1">
          <HistoryList controller={controller} />
        </div>
      </div>

      {/* 收起按钮 */}
      <div className="border-t border-[#f0f0f0] p-4">
        <button
          onClick={() => controller.toggleSidebarCollapsed()}
          className="w-full flex items-center text-[#595959] transition-colors hover:text-[#1890ff]"
        >
          <ChevronLeft className="size-4" />
          <span className="ml-2">收起侧栏</span>
        </button>
      </div>
    </div>
  );
});

// 主侧边栏组件
export const Sidebar: FC<SidebarProps> = memo(({ controller }) => {
  const collapsed = useObservableState(controller.sideBarCollapsed$);

  // 根据折叠状态选择不同的侧边栏组件
  return collapsed ? (
    <CollapsedSidebar controller={controller} />
  ) : (
    <ExpandedSidebar controller={controller} />
  );
});

import { BehaviorSubject } from 'rxjs';
import { toast } from 'sonner';

import type { HomeController } from '../HomeController';

export class SidebarController {
  // Sidebar状态
  sideBarCollapsed$ = new BehaviorSubject<boolean>(false);
  historyItems$ = new BehaviorSubject<HistoryItem[]>([]);
  historyLoading$ = new BehaviorSubject<boolean>(false);
  storagePermissionGranted$ = new BehaviorSubject<boolean>(false);
  storageDirectoryPath$ = new BehaviorSubject<string>('');
  storageInitializing$ = new BehaviorSubject<boolean>(true);

  private homeController: HomeController;

  constructor(homeController: HomeController) {
    this.homeController = homeController;
    // 初始化存储服务和历史记录
    this.loadHistoryItems();
  }

  // 加载历史记录
  loadHistoryItems = async () => {
    this.historyLoading$.next(true);
    // TODO 加载历史
    this.historyItems$.next(items);
    this.historyLoading$.next(false);
  };

  // 加载历史记录项目
  loadHistoryItem = async (id: string) => {
    this.historyLoading$.next(true);

    const result = await this.historyService.loadHistoryItem(id);

    if (result.success && result.data) {
      // 通过HomeController处理视频载入
      this.homeController.analysisVideo(result.data.videoFile, result.data.saveData);
      this.sideBarCollapsed$.next(true);
      toast.success('分析记录加载成功');
    } else {
      toast.error(result.message || '加载历史记录失败');
    }

    this.historyLoading$.next(false);
  };

  // 保存当前分析
  saveCurrentAnalysis = async (videoFile: File, analysisResult: any) => {
    if (!videoFile || !analysisResult) {
      toast.error('没有可保存的分析结果');
      return false;
    }

    // 检查存储权限
    if (!this.storagePermissionGranted$.getValue()) {
      const hasPermission = await this.requestStorageDirectory();
      if (!hasPermission) {
        return false;
      }
    }

    toast.info('正在保存分析结果，请勿关闭页面...');

    const result = await this.historyService.saveCurrentAnalysis(videoFile, analysisResult);

    if (result.success) {
      toast.success('分析结果已保存到本地');
      this.loadHistoryItems();
      return true;
    } else {
      toast.error(result.message || '保存分析结果失败');
      return false;
    }
  };

  // 删除历史记录项目
  deleteHistoryItem = async (id: string) => {
    const result = await this.historyService.deleteHistoryItem(id);

    if (result.success) {
      toast.success('历史记录已删除');
      this.loadHistoryItems();
      return true;
    } else {
      toast.error(result.message || '删除历史记录失败');
      return false;
    }
  };

  // 验证存储权限
  verifyStoragePermission = async () => {
    const result = await this.storageService.verifyStoragePermission();

    if (result.success) {
      this.storagePermissionGranted$.next(!!result.data);
      return !!result.data;
    } else {
      this.storagePermissionGranted$.next(false);
      return false;
    }
  };

  // 处理新分析
  handleNewAnalysis = () => {
    // 如果侧边栏已折叠，则展开
    if (this.sideBarCollapsed$.getValue()) {
      this.sideBarCollapsed$.next(false);
    }
    this.homeController.handleNewAnalysis();
  };

  // 切换侧边栏折叠状态
  toggleSidebarCollapsed = () => {
    const newValue = !this.sideBarCollapsed$.getValue();
    this.sideBarCollapsed$.next(newValue);
  };

  // 展开侧边栏并加载历史记录
  expandAndLoadHistory = () => {
    this.sideBarCollapsed$.next(false);
    this.loadHistoryItems();
  };

  // 销毁
  destroy() {
    // 完成所有观察对象
    this.sideBarCollapsed$.complete();
    this.historyItems$.complete();
    this.historyItems$.next([]);
    this.historyLoading$.complete();
    this.storagePermissionGranted$.complete();
    this.storageDirectoryPath$.complete();
    this.storageInitializing$.complete();

    // 清理引用
    this.homeController = null!;

    // 清理服务，先销毁historyService，再销毁storageService
    this.historyService.destroy();
    this.storageService.destroy();
  }
}

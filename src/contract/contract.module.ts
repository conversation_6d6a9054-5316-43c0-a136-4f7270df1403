import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { LlmModule } from '../llm/llm.module';
import { PromptTemplateModule } from '../prompt-template/prompt-template.module';

import { ContractHistoryService } from './contract-history.service';
import { ContractTemplatesService } from './contract-templates.service';
import { ContractController } from './contract.controller';
import { ContractService } from './contract.service';
import { ContractHistory } from './entities/contract-history.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ContractHistory]),
    PromptTemplateModule,
    LlmModule,
  ],
  controllers: [ContractController],
  providers: [ContractService, ContractTemplatesService, ContractHistoryService],
  exports: [ContractService, ContractTemplatesService, ContractHistoryService],
})
export class ContractModule {}

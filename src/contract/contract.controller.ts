import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Sse,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResultResponse } from '../common/utils/result.util';

import { ContractHistoryService } from './contract-history.service';
import { ContractService } from './contract.service';
import { ContractGenerateDto } from './dto';
import { HistoryQueryDto, UpdateHistoryTitleDto } from './dto/contract-history.dto';

@ApiTags('合同生成')
@Controller('contract')
@UseGuards(JwtAuthGuard)
export class ContractController {
  constructor(
    private readonly contractService: ContractService,
    private readonly contractHistoryService: ContractHistoryService,
  ) {}

  /**
   * 建立SSE连接，传递sessionId
   * @param sessionId 会话ID
   * @returns SSE流
   */
  @Sse('stream')
  @ApiOperation({ summary: '建立SSE连接，等待任务提交' })
  @ApiQuery({
    name: 'sessionId',
    required: true,
    description: '会话ID,用于SSE通知',
  })
  contractStream(@Query('sessionId') sessionId: string) {
    return this.contractService.createSseSession(sessionId);
  }

  /**
   * 提交合同生成任务
   * @param req 请求对象，包含用户信息
   * @param body 合同生成请求数据（包含sessionId和userInput）
   * @returns 提交结果
   */
  @Post('generate_contract')
  @ApiOperation({ summary: '提交合同生成任务' })
  @ApiBody({ type: ContractGenerateDto })
  async submitGenerateTask(
    @Req() req: Request & { user: { userId: number } },
    @Body() body: ContractGenerateDto,
  ): Promise<ResultResponse> {
    return this.contractService.submitTaskToSession(
      body.sessionId,
      body.userInput,
      req.user.userId,
    );
  }

  /**
   * 分页获取历史记录列表（支持过滤，只返回简要信息，基于用户权限）
   * @param req 请求对象，包含用户信息
   * @param queryDto 查询参数（包含分页和过滤条件）
   * @returns 分页历史记录列表
   */
  @Post('history')
  @ApiOperation({ summary: '分页获取合同生成历史记录列表（支持过滤）' })
  @ApiBody({ type: HistoryQueryDto })
  async getHistoryList(
    @Req() req: Request & { user: { userId: number } },
    @Body() queryDto: HistoryQueryDto,
  ) {
    return this.contractHistoryService.findHistoryListWithFilter(req.user.userId, queryDto);
  }

  /**
   * 根据会话ID获取历史记录详情（返回完整信息，基于用户权限）
   * @param req 请求对象，包含用户信息
   * @param sessionId 会话ID
   * @returns 历史记录详情
   */
  @Get('history/:sessionId')
  @ApiOperation({ summary: '根据会话ID获取历史记录详情' })
  async getHistoryDetail(
    @Req() req: Request & { user: { userId: number } },
    @Param('sessionId') sessionId: string,
  ) {
    return this.contractHistoryService.findDetailBySessionId(req.user.userId, sessionId);
  }

  /**
   * 根据会话ID删除历史记录（基于用户权限）
   * @param req 请求对象，包含用户信息
   * @param sessionId 会话ID
   * @returns 删除结果
   */
  @Delete('history/:sessionId')
  @ApiOperation({ summary: '根据会话ID删除历史记录' })
  async deleteHistoryBySession(
    @Req() req: Request & { user: { userId: number } },
    @Param('sessionId') sessionId: string,
  ) {
    return this.contractHistoryService.deleteBySessionId(req.user.userId, sessionId);
  }

  /**
   * 根据会话ID更新历史记录标题（基于用户权限）
   * @param req 请求对象，包含用户信息
   * @param updateDto 更新数据
   * @returns 更新结果
   */
  @Patch('history/title')
  @ApiOperation({ summary: '根据会话ID更新历史记录标题' })
  @ApiBody({ type: UpdateHistoryTitleDto })
  async updateHistoryTitle(
    @Req() req: Request & { user: { userId: number } },
    @Body() updateDto: UpdateHistoryTitleDto,
  ) {
    return this.contractHistoryService.updateTitleBySessionId(
      req.user.userId,
      updateDto.sessionId,
      updateDto.title,
    );
  }
}
